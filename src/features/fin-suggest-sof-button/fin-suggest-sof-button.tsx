import { MarqueeText } from "@/components/marquee-text";
import { memo, type FC } from "react";
import { cn } from "@/lib/utils";
import { LoyaltyIcon } from "./loyalty-icon";
import "./styles.scss";
import {
  FinSuggestSOFButtonProps,
  OfferPromotionDetail,
  OfferStatus,
  ProductCode,
  PromotionType,
} from "./types";
import { useSuggestSOFButton } from "./useSuggestSOFButton";
import { getPromotionStyles, getPromotionText } from "./utils";
import { getAssetUrl } from "./constants";

const PromotionBadge: FC<{ promotionDetail: OfferPromotionDetail }> = ({
  promotionDetail,
}) => {
  const themeStyle = getPromotionStyles(promotionDetail?.optimal_promo?.type);
  const textStyle = getPromotionText(promotionDetail);
  return (
    <div className="fin-suggest-sof-button__promotion-badge">
      <div
        style={themeStyle as React.CSSProperties}
        className="fin-suggest-sof-button__promotion-badge-text"
      >
        {textStyle}
        {promotionDetail?.optimal_promo?.type ===
        PromotionType.PROMOTION_TYPE_LOYALTY_COIN ? (
          <LoyaltyIcon width={12} height={12} />
        ) : null}
      </div>
    </div>
  );
};

const Icon: FC<{ iconUrl?: string }> = ({ iconUrl }) => (
  <div className="fin-suggest-sof-button__icon">
    <img src={iconUrl} width={36} height={36} alt="icon" />
  </div>
);

export type ContentProps = {
  title: string;
  amount?: string;
  originAmount?: string;
  error?: string;
};
const Content: FC<{ displayInfo: ContentProps }> = ({ displayInfo }) => {
  return (
    <div className="fin-suggest-sof-button__content">
      <p className="fin-suggest-sof-button__title">{displayInfo?.title}</p>
      <MarqueeText duration={displayInfo?.error ? 2700 : 700} delay={1000}>
        {displayInfo?.error ? (
          <p className="fin-suggest-sof-button__error-message">
            {displayInfo.error}
          </p>
        ) : (
          <p className="fin-suggest-sof-button__amount">
            {displayInfo?.amount}
            {displayInfo?.originAmount ? (
              <span className="fin-suggest-sof-button__origin-amount">
                {displayInfo.originAmount}
              </span>
            ) : null}
          </p>
        )}
      </MarqueeText>
    </div>
  );
};

// Loading skeleton component
const LoadingSkeleton: FC = () => (
  <>
    <div
      style={{ "--animate-time": "5s" } as React.CSSProperties}
      className="fin-suggest-sof-button__icon-skeleton animate-shimmer"
    />
    <div className="fin-suggest-sof-button__content-wrapper-skeleton">
      <div className="fin-suggest-sof-button__title-skeleton animate-shimmer" />
      <div className="fin-suggest-sof-button__amount-skeleton animate-shimmer" />
    </div>
  </>
);

const ErrorContent: FC<{ error?: string }> = ({ error }) => {
  const displayInfo = {
    title: "Không khả dụng",
    error: "Không có đề xuất trả sau cho đơn hàng này",
    amount: "",
    originAmount: "",
  };

  return (
    <>
      <Icon
        iconUrl={getAssetUrl({
          asset: ProductCode.PAYLATER,
          isDarkMode: false,
        })}
      />
      <Content displayInfo={displayInfo} />
    </>
  );
};

// UI button component
const FinSuggestSOFButtonUI: FC<
  Omit<FinSuggestSOFButtonProps, "onPress" | "onInitial" | "onErrorCallback" | "fallback" | "options">
    & {
    displayOffer: any;
    isOfferAvailable: boolean;
    isLoading: boolean;
    error?: string;
    _handleOnClick: () => void;
  }
> = ({
  children,
  className,
  displayOffer,
  isOfferAvailable,
  isLoading,
  error,
  _handleOnClick,
  ...props
}) => {
  const isDisabled =
    props.disabled ||
    !!error ||
    !displayOffer ||
    !isOfferAvailable ||
    !!displayOffer?.display_info?.error_description;

  return (
    <>
      {children ? (
        children
      ) : (
        <>
          <button
            {...props}
            data-loading={isLoading}
            className={cn("fin-suggest-sof-button", className)}
            disabled={isDisabled}
            onClick={_handleOnClick}
          >
            {isLoading ? (
              <LoadingSkeleton />
            ) : (
              <>
                {error || !displayOffer ? (
                  <ErrorContent error={error} />
                ) : (
                  <>
                    {displayOffer?.promo_details?.optimal_promo ? (
                      <PromotionBadge
                        promotionDetail={displayOffer.promo_details}
                      />
                    ) : null}
                    <Icon
                      iconUrl={getAssetUrl({
                        asset: displayOffer.product_code,
                        isDarkMode: false,
                      })}
                    />
                    <Content
                      displayInfo={{
                        title: displayOffer.display_info.payment_term_text,
                        amount: displayOffer.display_info.charge_amount_text,
                        originAmount:
                          displayOffer.display_info.origin_amount_text,
                        error: displayOffer.display_info.error_description,
                      }}
                    />
                  </>
                )}
              </>
            )}
          </button>
        </>
      )}
    </>
  );
};

// Provider component that handles the fallback logic
const FinSuggestSOFButton: FC<FinSuggestSOFButtonProps> = ({
  onPress,
  onInitial,
  onErrorCallback,
  fallback,
  ...props
}) => {
  const { _handleOnClick, suggestOfferResponse, isLoading, error } =
    useSuggestSOFButton({
      onPress,
      onInitial,
      onErrorCallback,
      ...props,
    });

  const displayOffer = suggestOfferResponse?.offers?.[0] ?? null;
  const isOfferAvailable =
    displayOffer?.status === OfferStatus.OFFER_STATUS_AVAILABLE;
  const shouldShowFallback = !displayOffer || !isOfferAvailable || !!error;

  if (fallback && isLoading) {
    return null;
  }

  // If should show fallback and fallback is provided, return fallback
  if (shouldShowFallback && fallback) {
    return <>{fallback}</>;
  }

  // Otherwise return the button
  return (
    <FinSuggestSOFButtonUI
      {...props}
      displayOffer={displayOffer}
      isOfferAvailable={isOfferAvailable}
      isLoading={isLoading}
      error={error}
      _handleOnClick={_handleOnClick}
    />
  );
};

export default memo(FinSuggestSOFButton);
