import React from 'react';
import FinSuggestSOFButton from './fin-suggest-sof-button';

/**
 * Example usage of the FinSuggestSOFButton (Provider Pattern)
 *
 * The component now works as a provider that:
 * - Returns the fallback React Node when displayOffer is false or isOfferAvailable is false
 * - Returns the FinSuggestSOFButtonUI (button) when offer is available
 *
 * Architecture:
 * - FinSuggestSOFButton = FinSuggestSOFButtonProvider (main export)
 * - FinSuggestSOFButtonUI = Internal button component
 */
const ExampleUsage = () => {
  const orders = [
    {
      app_id: 1,
      amount: 100000,
    },
  ];

  const handlePress = (result?: any) => {
    console.log('Button pressed:', result);
  };

  const handleError = (error: string) => {
    console.error('Error occurred:', error);
  };

  const handleInitial = (response: any) => {
    console.log('Initial response:', response);
  };

  // Custom fallback component when offer is not available
  const CustomFallback = () => (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      padding: '12px',
      color: '#666',
      fontSize: '14px',
      border: '1px dashed #ccc',
      borderRadius: '8px',
      backgroundColor: '#f9f9f9'
    }}>
      <span>🚫</span>
      <span>No payment options available</span>
    </div>
  );

  // Alternative payment method fallback
  const AlternativePaymentFallback = () => (
    <div style={{
      padding: '16px',
      textAlign: 'center',
      backgroundColor: '#e3f2fd',
      borderRadius: '8px',
      border: '1px solid #2196f3'
    }}>
      <p style={{ margin: '0 0 8px 0', fontWeight: 'bold', color: '#1976d2' }}>
        💳 Alternative Payment
      </p>
      <p style={{ margin: '0', fontSize: '14px', color: '#666' }}>
        Use traditional payment methods
      </p>
    </div>
  );

  return (
    <div>
      <h2>FinSuggestSOFButtonProvider Examples</h2>
      <p>The provider automatically returns either the fallback or the button based on offer availability.</p>

      {/* Example 1: With custom fallback component */}
      <div style={{ marginBottom: '24px' }}>
        <h3>Example 1: Custom Fallback Component</h3>
        <p>When displayOffer is false or isOfferAvailable is false, shows custom fallback:</p>
        <FinSuggestSOFButton
          orders={orders}
          onPress={handlePress}
          onErrorCallback={handleError}
          onInitial={handleInitial}
          fallback={<CustomFallback />}
        />
      </div>

      {/* Example 2: With alternative payment fallback */}
      <div style={{ marginBottom: '24px' }}>
        <h3>Example 2: Alternative Payment Fallback</h3>
        <p>Shows an alternative payment option when SOF is not available:</p>
        <FinSuggestSOFButton
          orders={orders}
          onPress={handlePress}
          onErrorCallback={handleError}
          onInitial={handleInitial}
          fallback={<AlternativePaymentFallback />}
        />
      </div>

      {/* Example 3: With simple text fallback */}
      <div style={{ marginBottom: '24px' }}>
        <h3>Example 3: Simple Text Fallback</h3>
        <FinSuggestSOFButton
          orders={orders}
          onPress={handlePress}
          onErrorCallback={handleError}
          onInitial={handleInitial}
          fallback={<span style={{ color: '#999', fontStyle: 'italic' }}>Payment option unavailable</span>}
        />
      </div>

      {/* Example 4: Without fallback (uses default ErrorContent inside button) */}
      <div style={{ marginBottom: '24px' }}>
        <h3>Example 4: Without Fallback (Default Behavior)</h3>
        <p>Returns the button with internal error handling:</p>
        <FinSuggestSOFButton
          orders={orders}
          onPress={handlePress}
          onErrorCallback={handleError}
          onInitial={handleInitial}
        />
      </div>
    </div>
  );
};

export default ExampleUsage;
