# FinSuggestSOFButton Component (Provider Pattern)

A React component that displays financial service offers with promotional badges and handles user interactions for selecting source of funding (SOF) options. Now implemented using the provider pattern for better separation of concerns and flexible fallback handling.

## Architecture

The component follows a provider pattern with clear separation:

```
FinSuggestSOFButton (Provider)
├── FinSuggestSOFButtonProvider - Main logic and fallback handling
└── FinSuggestSOFButtonUI - Pure UI button component
```

## Features

- **Provider Pattern**: Clean separation between logic and UI components
- **Flexible Fallbacks**: Custom React nodes when offers are unavailable
- **Automatic offer fetching**: Fetches available fin offers on component mount
- **Promotional badges**: Displays discount, cashback, or loyalty coin promotions
- **Loading states**: Shows skeleton loading animation while fetching data
- **Error handling**: Graceful error states with fallback messaging
- **Customizable styling**: CSS variables for theming and custom styles
- **Marquee text animation**: Scrolling text for long content
- **TypeScript support**: Full type definitions included

## Installation

### Module Federation

The FinSuggestSOFButton component is available through Module Federation for different environments:

#### Development Environment
```javascript
// webpack.config.js or module federation configuration
const ModuleFederationPlugin = require('@module-federation/webpack');

module.exports = {
  plugins: [
    new ModuleFederationPlugin({
      remotes: {
        fin_sdk: 'https://socialdev.zalopay.vn/mfpublic/share-module/micro-app/bd211552-0808-4858-b5e1-fee2f9c677f7'
      }
    })
  ]
};
```

#### Usage in your application
```tsx
// Import the component from the federated module
import FinSuggestSOFButton from 'fin_sdk/FinSuggestSOFButton';
```

#### Environment URLs

| Environment | Status | Module Name | Component Path | Remote URL |
|-------------|--------|-------------|----------------|------------|
| **dev** | ✅ Available | `fin_sdk` | `./FinSuggestSOFButton` | `https://socialdev.zalopay.vn/mfpublic/share-module/micro-app/bd211552-0808-4858-b5e1-fee2f9c677f7` |
| **stg** | 🚧 WIP | `fin_sdk` | `./FinSuggestSOFButton` | Coming soon |
| **prod** | 🚧 WIP | `fin_sdk` | `./FinSuggestSOFButton` | Coming soon |

#### Notes
- Currently only the development environment is available
- Staging and production environments are work in progress
- Make sure your webpack configuration supports Module Federation
- The component path within the federated module is `./FinSuggestSOFButton`

## Basic Usage

```tsx
import FinSuggestSOFButton from '@/features/fin-suggest-sof-button';
import type { SuggestSOFCallbackData, OrderItem } from '@/features/fin-suggest-sof-button';

const orders: OrderItem[] = [
  { appId: "app123", amount: 100000 },
  { appId: "app456", amount: 50000 }
];

const handleOfferSelect = (result: SuggestSOFCallbackData) => {
  console.log('Selected offer:', result);
  // Handle the selected offer
  // result contains: { specified_sof: { pmc_id: string, partner_code: string } }
};

const handleInitialData = (data: SuggestOfferResponse) => {
  console.log('Initial offer data:', data);
  // Optional: Handle initial data when offers are first loaded
};

const handleError = (error: string) => {
  console.error('Error fetching offers:', error);
  // Optional: Handle errors (e.g., show notification, log to analytics)
};

function MyComponent() {
  return (
    <FinSuggestSOFButton
      orders={orders}
      onPress={handleOfferSelect}
      onInitial={handleInitialData}
      onErrorCallback={handleError}
    />
  );
}
```

## Props

### Required Props

| Prop | Type | Description |
|------|------|-------------|
| `orders` | `OrderItem[]` | Array of order items with appId and amount |
| `onPress` | `(result: SuggestSOFCallbackData) => void` | Callback when user selects an offer |

### Optional Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `fallback` | `ReactNode` | - | **NEW**: Custom React node to render when offer is unavailable |
| `onInitial` | `(data: SuggestOfferResponse) => void` | - | Callback when initial offer data is loaded |
| `onErrorCallback` | `(error: string) => void` | - | Callback when an error occurs during offer fetching |
| `children` | `ReactNode` | - | Custom content to display instead of default layout |
| `disabled` | `boolean` | `false` | Disable the button |
| `className` | `string` | - | Additional CSS classes |
| `options` | `any` | - | Additional options (WIP) |

All standard HTML button attributes are also supported.

## Types

### OrderItem
```tsx
type OrderItem = {
  appId: string;
  amount: number;
};
```

### SuggestSOFCallbackData
```tsx
type SuggestSOFCallbackData = {
  specified_sof: {
    pmc_id: string;
    partner_code: string;
  };
};
```

### SuggestOfferResponse
```tsx
type SuggestOfferResponse = {
  offers: Array<Offer>;
  issued_at: string;
};
```

## Fallback Functionality

The provider returns the fallback when:
- `displayOffer` is `false` (no offer available)
- `isOfferAvailable` is `false` (offer status is not "OFFER_STATUS_AVAILABLE")
- AND `fallback` prop is provided

### Basic Fallback Usage
```tsx
<FinSuggestSOFButton
  orders={orders}
  onPress={handleOfferSelect}
  fallback={<div>Payment option not available</div>}
/>
```

### Custom Fallback Component
```tsx
const CustomFallback = () => (
  <div className="payment-unavailable">
    <span>🚫</span>
    <span>No payment options available</span>
    <button onClick={() => window.location.href = '/payment'}>
      Use Traditional Payment
    </button>
  </div>
);

<FinSuggestSOFButton
  orders={orders}
  onPress={handleOfferSelect}
  fallback={<CustomFallback />}
/>
```

### Without Fallback (Default Behavior)
```tsx
<FinSuggestSOFButton
  orders={orders}
  onPress={handleOfferSelect}
  // No fallback prop - uses internal ErrorContent within button
/>
```

## Advanced Usage

### Custom Content
```tsx
<FinSuggestSOFButton
  orders={orders}
  onPress={handleOfferSelect}
>
  <div className="custom-content">
    <span>Custom offer display</span>
  </div>
</FinSuggestSOFButton>
```

### Using Compound Components
```tsx
<FinSuggestSOFButton
  orders={orders}
  onPress={handleOfferSelect}
>
  <FinSuggestSOFButton.PromotionBadge promotionDetail={promotionData} />
  <FinSuggestSOFButton.Icon iconUrl="https://example.com/icon.png" />
  <FinSuggestSOFButton.OfferInfo displayInfo={displayData} />
</FinSuggestSOFButton>
```

## Styling

The component uses CSS variables for theming. You can customize these in your CSS:

```css
.fin-suggest-sof-button {
  --primary-bg-color: #your-color;
  --primary-text-color: #your-text-color;
  --primary-ring-color: #your-ring-color;
  --promotion-badge-text-background-color: #your-badge-color;
}
```

## Button States

The component handles several states automatically:

- **Loading**: Shows skeleton animation while fetching offers
- **Error**: Displays error message when API fails
- **No offers**: Shows "Không có đề xuất trả góp" message
- **Disabled**: Button is disabled when no valid offers or has errors
- **Active**: Normal interactive state with offer data

## Promotion Types

The component supports three promotion types:

- `PROMOTION_TYPE_DISCOUNT`: Shows "Giảm {amount}" with red badge
- `PROMOTION_TYPE_CASHBACK`: Shows "Hoàn {amount}" with green badge  
- `PROMOTION_TYPE_LOYALTY_COIN`: Shows "+{amount} xu" with orange badge and coin icon

## Testing

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import FinSuggestSOFButton from '@/features/fin-suggest-sof-button';

test('calls onPress when button is clicked', () => {
  const handleClick = jest.fn();
  const orders = [{ appId: "123", amount: 100 }];
  
  render(
    <FinSuggestSOFButton 
      orders={orders} 
      onPress={handleClick}
    />
  );
  
  const button = screen.getByRole('button');
  fireEvent.click(button);
  
  expect(handleClick).toHaveBeenCalled();
});
```