import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import "@testing-library/jest-dom";

import { render, screen, fireEvent } from "@testing-library/react";

import FinSuggestSOFButton from "../fin-suggest-sof-button";
import * as hooks from "../useSuggestSOFButton";
import { mockOfferResponse } from "../mock";
import { Offer } from "../types";

const mockOrders = [
  { app_id: 123, amount: 100 },
  { app_id: 456, amount: 200 },
];

const mockHandleClick = vi.fn();
const mockInternalHandler = vi.fn();
const mockOnInitial = vi.fn();
const mockOnErrorCallback = vi.fn();

beforeEach(() => {
  vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
    _handleOnClick: mockInternalHandler,
    suggestOfferResponse: mockOfferResponse,
    isLoading: false,
    error: undefined,
    debouncedInitial: vi.fn(),
  }));
});

afterEach(() => {
  vi.clearAllMocks();
});

describe("FinSuggestSOFButton", () => {
  it("renders offer content when suggestOfferResponse is available", () => {
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    // Should show the payment term text from the offer
    expect(
      screen.getByText(mockOfferResponse.offers?.[0]?.display_info.payment_term_text || "")
    ).toBeInTheDocument();
    
    // Should show the charge amount text
    expect(
      screen.getByText(mockOfferResponse.offers?.[0]?.display_info.charge_amount_text || "")
    ).toBeInTheDocument();
  });

  it("renders children when provided", () => {
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick}>
        <span>Custom Content</span>
      </FinSuggestSOFButton>
    );
    
    expect(screen.getByText("Custom Content")).toBeInTheDocument();
    // Should not show default offer content when children are provided
    expect(
      screen.queryByText(mockOfferResponse.offers?.[0]?.display_info.payment_term_text || "")
    ).not.toBeInTheDocument();
  });

  it("calls onPress when clicked", () => {
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick}>
        Test Button
      </FinSuggestSOFButton>
    );
    const button = screen.getByRole("button");
    fireEvent.click(button);
    expect(mockInternalHandler).toHaveBeenCalledTimes(1);
  });

  it("spreads additional props to button element", () => {
    render(
      <FinSuggestSOFButton
        orders={mockOrders}
        onPress={mockHandleClick}
        data-testid="test-button"
        aria-label="test button"
      >
        Test Button
      </FinSuggestSOFButton>
    );
    const button = screen.getByRole("button");
    expect(button).toHaveAttribute("data-testid", "test-button");
    expect(button).toHaveAttribute("aria-label", "test button");
  });

  it("shows loading skeleton when isLoading is true", () => {
    vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
      _handleOnClick: mockInternalHandler,
      suggestOfferResponse: undefined,
      isLoading: true,
      error: undefined,
      debouncedInitial: vi.fn(),
    }));
    
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    const button = screen.getByRole("button");
    expect(button).toHaveAttribute("data-loading", "true");
    expect(button.querySelector(".fin-suggest-sof-button__icon-skeleton")).toBeInTheDocument();
  });

  it("shows error content when error occurs", () => {
    const errorMessage = "Failed to fetch offer";
    vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
      _handleOnClick: mockInternalHandler,
      suggestOfferResponse: undefined,
      isLoading: false,
      error: errorMessage,
      debouncedInitial: vi.fn(),
    }));
    
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    expect(screen.getByText("Không khả dụng")).toBeInTheDocument();
    expect(screen.getByText("Không có đề xuất trả sau cho đơn hàng này")).toBeInTheDocument();
  });

  it("renders error when no offer", () => {
    vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
      _handleOnClick: mockInternalHandler,
      suggestOfferResponse: undefined,
      isLoading: false,
      error: undefined,
      debouncedInitial: vi.fn(),
    }));
    
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    const button = screen.getByRole("button");
    // Button should be present but empty (no content)
    expect(button).toBeInTheDocument();
    expect(screen.getByText("Không có đề xuất trả sau cho đơn hàng này")).toBeInTheDocument();
  });

  it("is disabled when error occurs", () => {
    vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
      _handleOnClick: mockInternalHandler,
      suggestOfferResponse: undefined,
      isLoading: false,
      error: "Some error",
      debouncedInitial: vi.fn(),
    }));
    
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
  });

  it("is disabled when no offer is available", () => {
    vi.spyOn(hooks, "useSuggestSOFButton").mockImplementation(() => ({
      _handleOnClick: mockInternalHandler,
      suggestOfferResponse: undefined,
      isLoading: false,
      error: undefined,
      debouncedInitial: vi.fn(),
    }));
    
    render(
      <FinSuggestSOFButton orders={mockOrders} onPress={mockHandleClick} />
    );
    
    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
  });

  it("passes onInitial and onErrorCallback to hook", () => {
    render(
      <FinSuggestSOFButton 
        orders={mockOrders} 
        onPress={mockHandleClick}
        onInitial={mockOnInitial}
        onErrorCallback={mockOnErrorCallback}
      />
    );
    
    expect(hooks.useSuggestSOFButton).toHaveBeenCalledWith(
      expect.objectContaining({
        onPress: mockHandleClick,
        onInitial: mockOnInitial,
        onErrorCallback: mockOnErrorCallback,
        orders: mockOrders,
      })
    );
  });
});
